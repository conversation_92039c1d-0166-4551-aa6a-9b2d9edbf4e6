#ifndef D3Q19_H_
#define D3Q19_H_

#include <AMReX_Array4.H>

#define PI (3.141592653589793238462643383279)

using namespace amrex;

#define DIM  3
#define Q  19

const int coarsest_level = 0;
const int max_ref_level  = 0;
const int rate  = (1 << max_ref_level);

const Real D = 16.0;
const Real R = D / 2.0;

// 扩大计算域以适应周期性边界条件和更多颗粒
const int NX = 24 * D;  // 从12D增加到24D
const int NY = 24 * D;  // 从12D增加到24D
const int NZ = 24 * D;  // 从48D减少到24D，使计算域更接近立方体

const Real nx = NX;
const Real ny = NY;
const Real nz = NZ;

// 湍流参数设置 - 泰勒雷诺数约100
#define Re_lambda  100.0    // 泰勒雷诺数
#define Re    150.0         // 基于积分尺度的雷诺数
#define dx_0  (nx / NX)     // cm
#define dt_0  dx_0          // s
#define c     1.0           // cm/s
#define cs    (c / sqrt(3.0))
#define cs2   (cs * cs)
#define Ma    (0.1)         // 增大马赫数以获得更强的湍流

#define rho0  (1.0)         // g/cm3
#define p0    (rho0 * cs2)
// 根据湍流雷诺数调整粘度
#define u_rms (0.1 * cs)    // 湍流脉动速度
#define lambda_taylor (D * 0.5)  // 泰勒微尺度
#define mv_0  (u_rms * lambda_taylor / Re_lambda)  // 运动粘度
#define tau_0 (mv_0 / (cs2 * dt_0) + 0.5)
#define U0    (u_rms)       // 特征速度设为湍流脉动速度

#define G     (0.000939018)//这里不用缩放

#define rhof    rho0
#define rhop    (1.14)
#define volume  (PI * D*dx_0 * D*dx_0 * D*dx_0 / 6.0)
#define Mp      (rhop * volume)    //g
#define Mf      (rhof * volume)    //g
#define Mp_iner (Mp * D*dx_0 * D*dx_0 / 10.0)  //moment of particle  inertia
#define Mf_iner (Mf * D*dx_0 * D*dx_0 / 10.0)  //virtual moment of fluid inertia inside particle

const Real  X  = NX / 2.0;
const Real  Y  = NY / 2.0;
const Real  Z  = 120 * D;
const Real center[DIM] = {X, Y, Z};

//全都转成真实大小
const Real dx_min = dx_0 / rate;
const Real dt_min = dt_0 / rate;

const Real rb  =  pow(((pow(rate*R, 3) + pow(rate*R-1, 3)) / 2.0), (1.0/3.0));//修正的半径

const Real ds0 = 1.0;
const int  ns  = PI * rb / ds0 + 1;   //表面分带数
const int  nt1 = 2 * PI * rb / ds0;   //赤道上point数量

const Real LP_dr   = 1.0;
const Real LP_area = 4 * PI * rb * rb;

//多颗粒相关的参数
const Real safe  = 0.05 * R;
const Real Epp1  = 0.5; //dx_min;
const Real Epp2  = 0.05; //dx_min * dx_min;
const Real Epw1  = 0.0125; //dx_min;
const Real Epw2  = 0.0025; //dx_min * dx_min;

//湍流相关参数
const Real k_max = 8.0;           // 最大波数
const Real k_min = 1.0;           // 最小波数
const Real energy_spectrum_const = 1.5;  // 能谱常数
const int n_modes = 64;           // 傅里叶模态数量


AMREX_GPU_DEVICE AMREX_FORCE_INLINE
IntVect e[Q] =
{
    {  0,  0,  0 },
    {  1,  0,  0 },
    { -1,  0,  0 },
    {  0,  1,  0 },
    {  0, -1,  0 },
    {  0,  0,  1 },
    {  0,  0, -1 },
    {  1,  1,  0 },
    { -1, -1,  0 },
    {  1, -1,  0 },
    { -1,  1,  0 },
    {  1,  0,  1 },
    { -1,  0, -1 },
    {  1,  0, -1 },
    { -1,  0,  1 },
    {  0,  1,  1 },
    {  0, -1, -1 },
    {  0,  1, -1 },
    {  0, -1,  1 }
};

AMREX_GPU_DEVICE AMREX_FORCE_INLINE
Real w[Q] =
{
    1./3.,
    1./18.,
    1./18.,
    1./18.,
    1./18.,
    1./18.,
    1./18.,
    1./36.,
    1./36.,
    1./36.,
    1./36.,
    1./36.,
    1./36.,
    1./36.,
    1./36.,
    1./36.,
    1./36.,
    1./36.,
    1./36.
};


AMREX_GPU_DEVICE AMREX_FORCE_INLINE
Real feqHeLuo(const Real& pressure, const RealVect& u, const int i)
{
    amrex::Real eu, uv, feqt;

    eu  = e[i][0] * u[0] + e[i][1] * u[1] + e[i][2] * u[2];

    uv  = (u[0] * u[0] + u[1] * u[1] + u[2] * u[2]);

    feqt = w[i] * (pressure + p0 * ( 3.0 * eu + 4.5 * eu * eu - 1.5 * uv));

    return feqt;
}

AMREX_GPU_DEVICE AMREX_FORCE_INLINE
Real forceGuo(const RealVect& u, const RealVect& Ft, const int i)           //怎么会出问题，太离谱了？
{
    amrex::Real m, n, nf;

    m = ((e[i][0] - u[0]) * Ft[0] + (e[i][1] - u[1]) * Ft[1] + (e[i][2] - u[2]) * Ft[2]) / cs2;

    n  = (e[i][0] * u[0] + e[i][1] * u[1] + e[i][2] * u[2]) / cs2;

    nf = (e[i][0] * Ft[0] + e[i][1] * Ft[1] + e[i][2] * Ft[2]) / cs2;

    return w[i] * (m + n * nf);
}



#endif