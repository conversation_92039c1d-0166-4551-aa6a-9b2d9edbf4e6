# 周期性边界条件和计算域扩大修改说明

## 修改目标
1. 将边界条件从外推边界改为周期性边界条件
2. 扩大计算域以容纳更多颗粒
3. 改进颗粒分布使其在整个计算域内更均匀

## 主要修改内容

### 1. D3Q19.H - 计算域尺寸修改

#### 原始设置
```cpp
const int NX = 12 * D;  // 192
const int NY = 12 * D;  // 192
const int NZ = 48 * D;  // 768
```

#### 修改后设置
```cpp
const int NX = 24 * D;  // 384 (扩大2倍)
const int NY = 24 * D;  // 384 (扩大2倍)
const int NZ = 24 * D;  // 384 (缩小为立方体)
```

**优势**：
- 计算域从长方体改为立方体，更适合湍流研究
- 总体积增大约50%，可容纳更多颗粒
- 各方向尺寸相等，周期性边界条件更自然

### 2. main.cpp - 几何设置修改

#### 周期性边界条件设置
```cpp
// 原始：非周期性
{AMREX_D_DECL(0, 0, 0)}

// 修改后：全周期性
{AMREX_D_DECL(1, 1, 1)}  // 所有方向为周期性边界条件
```

### 3. AmrCoreLBM.cpp - 边界条件修改

#### 边界类型修改
```cpp
// 原始：外推边界
int bc_lo[] = {BCType::foextrap, BCType::foextrap, BCType::foextrap};
int bc_hi[] = {BCType::foextrap, BCType::foextrap, BCType::foextrap};

// 修改后：周期性边界
int bc_lo[] = {BCType::int_dir, BCType::int_dir, BCType::int_dir};
int bc_hi[] = {BCType::int_dir, BCType::int_dir, BCType::int_dir};
```

#### Boundary函数简化
```cpp
void AmrCoreLBM::Boundary(int lev)
{
    // 使用AMReX内置的周期性边界处理
    amrex::MultiFab& f_old_lev = f_old[lev];
    amrex::MultiFab& f_new_lev = f_new[lev];

    f_old_lev.FillBoundary(geom[lev].periodicity());
    f_new_lev.FillBoundary(geom[lev].periodicity());
}
```

### 4. InitParticles.H - 颗粒分布改进

#### 新的均匀分布算法
```cpp
// 计算每个方向上可以放置的颗粒数量
amrex::Real min_spacing = 2.5 * D;  // 颗粒间最小间距

int n_x = static_cast<int>((NX - 2 * D) / min_spacing);
int n_y = static_cast<int>((NY - 2 * D) / min_spacing);
int n_z = static_cast<int>((NZ - 2 * D) / min_spacing);

// 确保颗粒数量不超过500
int total_particles = n_x * n_y * n_z;
if(total_particles > 500) {
    amrex::Real scale = std::pow(500.0 / total_particles, 1.0/3.0);
    n_x = static_cast<int>(n_x * scale);
    n_y = static_cast<int>(n_y * scale);
    n_z = static_cast<int>(n_z * scale);
}
```

#### 三维均匀分布
- 原来：5层分布，每层10x10
- 现在：三维网格分布，自动计算最优排列
- 颗粒间距：2.5D（保证不重叠）
- 分布范围：整个计算域（除边界缓冲区）

### 5. Kernels.H - 边界处理函数修改

#### fill_boundary函数
- 原来：手动处理6个面的边界条件
- 现在：注释掉手动处理，使用AMReX自动周期性处理
- 简化为直接返回，AMReX自动处理周期性边界

### 6. inputs - 仿真参数调整

#### 主要参数修改
```
max_step: 100000 → 50000 (适应更大计算域)
amr.regrid_int: 5 → 10 (周期性边界更稳定)
amr.grid_eff: 0.7 → 0.8 (提高网格效率)
amr.max_grid_size: 32 → 64 (适应更大计算域)
amr.plot_file: turbulent_500_particle_ → periodic_turbulent_500_particle_
amr.plot_int: 200 → 500 (适当增加输出间隔)
lbm.err: [0.4,0.6,0.8,1.0] → [0.3,0.5,0.7,0.9] (调整误差阈值)
```

## 技术优势

### 1. 周期性边界条件
- **物理意义**：模拟无限大湍流场
- **数值稳定**：消除边界反射和人工边界效应
- **计算效率**：无需复杂的边界处理
- **湍流特性**：保持湍流的统计均匀性

### 2. 扩大的计算域
- **更多颗粒**：可容纳更多颗粒进行统计分析
- **更好的湍流**：更大的积分尺度和更丰富的涡结构
- **减少有限尺寸效应**：降低计算域尺寸对结果的影响

### 3. 均匀颗粒分布
- **统计均匀性**：颗粒在整个域内均匀分布
- **避免聚集**：防止颗粒初始聚集影响结果
- **更好的采样**：在不同湍流区域都有颗粒采样

## 预期效果

### 1. 流场特征
- 真正的周期性湍流场
- 无边界效应的湍流发展
- 更自然的湍流统计特性

### 2. 颗粒行为
- 颗粒可以自由穿越边界
- 更真实的颗粒-湍流相互作用
- 更好的颗粒统计特性

### 3. 计算性能
- 简化的边界处理
- 更高的并行效率
- 更稳定的数值计算

## 验证建议

1. **检查周期性**：验证流场在边界处的连续性
2. **颗粒分布**：确认颗粒初始分布的均匀性
3. **湍流统计**：检查湍流统计量的空间均匀性
4. **能量守恒**：验证系统能量守恒
5. **长时间稳定性**：检查长时间运行的数值稳定性

## 注意事项

1. **内存需求**：计算域扩大约50%，内存需求相应增加
2. **计算时间**：网格点数增加，计算时间会增长
3. **并行效率**：需要调整并行参数以获得最佳性能
4. **输出文件**：输出文件会更大，需要足够的存储空间

## 验证方法

### 编译和运行验证程序
```bash
g++ -o verify_periodic_setup verify_periodic_setup.cpp -lm
./verify_periodic_setup
```

### 验证内容
1. **计算域尺寸**：确认新的计算域尺寸
2. **颗粒分布**：检查颗粒数量和分布均匀性
3. **颗粒间距**：验证颗粒间最小距离
4. **湍流参数**：确认湍流相关参数设置
5. **资源估算**：评估计算和内存需求

## 编译和运行

### 编译主程序
```bash
make clean
make
```

### 运行仿真
```bash
mpirun -np [进程数] ./main3d.gnu.MPI.ex inputs
```

建议进程数：根据验证程序输出的建议值

## 文件修改清单

1. **D3Q19.H**: 计算域尺寸
2. **main.cpp**: 几何设置和周期性边界
3. **AmrCoreLBM.cpp**: 边界条件和边界处理
4. **InitParticles.H**: 颗粒分布算法
5. **Kernels.H**: 边界处理函数
6. **inputs**: 仿真参数
7. **周期性边界修改说明.md**: 本说明文档
8. **verify_periodic_setup.cpp**: 验证程序
