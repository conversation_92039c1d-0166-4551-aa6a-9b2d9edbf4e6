#ifndef KERNELS_H_
#define KERNELS_H_

#include <AMReX_Box.H>
#include <AMReX_FArrayBox.H>
#include <AMReX_Geometry.H>
#include <AMReX_PhysBCFunct.H>
#include <cmath>
#include <random>

#include "D3Q19.H"

using namespace amrex;

// 湍流速度场生成函数
AMREX_GPU_HOST_DEVICE
AMREX_FORCE_INLINE
Real turbulent_velocity_x(int i, int j, int k)
{
    // 使用简化的湍流模型生成x方向速度分量
    Real x = i * dx_0;
    Real y = j * dx_0;
    Real z = k * dx_0;

    // 多模态叠加生成湍流速度场
    Real u_turb = 0.0;
    for(int n = 1; n <= 8; n++)
    {
        Real kx = 2.0 * PI * n / nx;
        Real ky = 2.0 * PI * n / ny;
        Real kz = 2.0 * PI * n / nz;

        // 简化的能谱分布 E(k) ~ k^(-5/3)
        Real k_mag = sqrt(kx*kx + ky*ky + kz*kz);
        Real amplitude = u_rms * pow(k_mag, -5.0/6.0) / n;

        // 相位随机化（简化版本）
        Real phase_x = 0.1 * n * (i + j + k);
        Real phase_y = 0.2 * n * (i - j + k);
        Real phase_z = 0.3 * n * (i + j - k);

        u_turb += amplitude * (sin(kx*x + phase_x) * cos(ky*y + phase_y) * sin(kz*z + phase_z));
    }

    return u_turb;
}

AMREX_GPU_HOST_DEVICE
AMREX_FORCE_INLINE
Real turbulent_velocity_y(int i, int j, int k)
{
    Real x = i * dx_0;
    Real y = j * dx_0;
    Real z = k * dx_0;

    Real v_turb = 0.0;
    for(int n = 1; n <= 8; n++)
    {
        Real kx = 2.0 * PI * n / nx;
        Real ky = 2.0 * PI * n / ny;
        Real kz = 2.0 * PI * n / nz;

        Real k_mag = sqrt(kx*kx + ky*ky + kz*kz);
        Real amplitude = u_rms * pow(k_mag, -5.0/6.0) / n;

        Real phase_x = 0.15 * n * (i + j + k);
        Real phase_y = 0.25 * n * (i - j + k);
        Real phase_z = 0.35 * n * (i + j - k);

        v_turb += amplitude * (cos(kx*x + phase_x) * sin(ky*y + phase_y) * cos(kz*z + phase_z));
    }

    return v_turb;
}

AMREX_GPU_HOST_DEVICE
AMREX_FORCE_INLINE
Real turbulent_velocity_z(int i, int j, int k)
{
    Real x = i * dx_0;
    Real y = j * dx_0;
    Real z = k * dx_0;

    Real w_turb = 0.0;
    for(int n = 1; n <= 8; n++)
    {
        Real kx = 2.0 * PI * n / nx;
        Real ky = 2.0 * PI * n / ny;
        Real kz = 2.0 * PI * n / nz;

        Real k_mag = sqrt(kx*kx + ky*ky + kz*kz);
        Real amplitude = u_rms * pow(k_mag, -5.0/6.0) / n;

        Real phase_x = 0.12 * n * (i + j + k);
        Real phase_y = 0.22 * n * (i - j + k);
        Real phase_z = 0.32 * n * (i + j - k);

        w_turb += amplitude * (sin(kx*x + phase_x) * sin(ky*y + phase_y) * cos(kz*z + phase_z));
    }

    return w_turb;
}

// 改进的湍流初始化函数，考虑散度自由条件
AMREX_GPU_HOST_DEVICE
AMREX_FORCE_INLINE
void generate_divergence_free_turbulence(int i, int j, int k, Real& u_turb, Real& v_turb, Real& w_turb)
{
    // 使用势函数方法生成散度自由的湍流场
    Real x = i * dx_0;
    Real y = j * dx_0;
    Real z = k * dx_0;

    u_turb = 0.0;
    v_turb = 0.0;
    w_turb = 0.0;

    // 多模态叠加，确保散度为零
    for(int n = 1; n <= 6; n++)
    {
        Real kx = 2.0 * PI * n / nx;
        Real ky = 2.0 * PI * n / ny;
        Real kz = 2.0 * PI * n / nz;

        Real k_mag = sqrt(kx*kx + ky*ky + kz*kz);
        if(k_mag < 1e-10) continue;

        // Kolmogorov能谱 E(k) ~ k^(-5/3)
        Real amplitude = u_rms * pow(k_mag, -5.0/6.0) / (n * n);

        // 不同的相位确保随机性
        Real phase1 = 0.1 * n * (i + 2*j + 3*k);
        Real phase2 = 0.2 * n * (2*i + j + 3*k);
        Real phase3 = 0.3 * n * (3*i + 2*j + k);

        // 使用旋度形式确保散度为零
        // u = ∂ψ/∂y - ∂φ/∂z
        // v = ∂φ/∂x - ∂ψ/∂z
        // w = ∂ψ/∂x - ∂φ/∂y

        Real psi = amplitude * sin(kx*x + phase1) * cos(ky*y + phase2) * sin(kz*z + phase3);
        Real phi = amplitude * cos(kx*x + phase1) * sin(ky*y + phase2) * cos(kz*z + phase3);

        // 计算偏导数
        Real dpsi_dy = -amplitude * ky * sin(kx*x + phase1) * sin(ky*y + phase2) * sin(kz*z + phase3);
        Real dphi_dz = -amplitude * kz * cos(kx*x + phase1) * sin(ky*y + phase2) * sin(kz*z + phase3);

        Real dphi_dx = -amplitude * kx * sin(kx*x + phase1) * sin(ky*y + phase2) * cos(kz*z + phase3);
        Real dpsi_dz = amplitude * kz * sin(kx*x + phase1) * cos(ky*y + phase2) * cos(kz*z + phase3);

        Real dpsi_dx = amplitude * kx * cos(kx*x + phase1) * cos(ky*y + phase2) * sin(kz*z + phase3);
        Real dphi_dy = amplitude * ky * cos(kx*x + phase1) * cos(ky*y + phase2) * cos(kz*z + phase3);

        u_turb += dpsi_dy - dphi_dz;
        v_turb += dphi_dx - dpsi_dz;
        w_turb += dpsi_dx - dphi_dy;
    }

    // 限制速度幅值以保持数值稳定性
    Real vel_mag = sqrt(u_turb*u_turb + v_turb*v_turb + w_turb*w_turb);
    if(vel_mag > 0.3 * cs)  // 限制在声速的30%以内
    {
        Real scale = 0.3 * cs / vel_mag;
        u_turb *= scale;
        v_turb *= scale;
        w_turb *= scale;
    }
}

AMREX_GPU_DEVICE
AMREX_FORCE_INLINE
void
init_fluid(int i, int j, int k, amrex::Array4<amrex::Real> const& fold,
           amrex::Array4<amrex::Real> const& fnew)
{
    // 生成散度自由的湍流速度场
    Real u_turb, v_turb, w_turb;
    generate_divergence_free_turbulence(i, j, k, u_turb, v_turb, w_turb);

    // 添加平均流动（可选）
    Real u_mean = 0.0;  // 可以设置为非零值添加平均流动
    Real v_mean = 0.0;
    Real w_mean = 0.0;

    Real u_total = u_mean + u_turb;
    Real v_total = v_mean + v_turb;
    Real w_total = w_mean + w_turb;

    for(int q = 0; q < Q; q++)
    {
        fold(i, j, k, q) = feqHeLuo(p0, {u_total, v_total, w_total}, q);
        fnew(i, j, k, q) = fold(i, j, k, q);
    }
}

AMREX_GPU_DEVICE
AMREX_FORCE_INLINE
void
compute_macro(int i, int j, int k, amrex::Array4<amrex::Real> const& fold,
              amrex::Array4<amrex::Real> const& rho, amrex::Array4<amrex::Real> const& u)
{
    Real uxt = 0.0, uyt = 0.0, uzt = 0.0, rhot = 0.0, ft = 0.0;

    for(int q = 0; q < Q; q++)
    {
        ft    = fold(i, j, k, q);
        rhot += ft;
        uxt  += ft * e[q][0];
        uyt  += ft * e[q][1];
        uzt  += ft * e[q][2];
    }

    u(i, j, k, 0)   = uxt / p0;
    u(i, j, k, 1)   = uyt / p0;
    u(i, j, k, 2)   = uzt / p0;
    rho(i, j, k, 0) = rhot;
}


AMREX_GPU_DEVICE
AMREX_FORCE_INLINE
void
compute_vorticity(int i, int j, int k, amrex::Array4<amrex::Real> const& u,
                  amrex::Array4<amrex::Real> const& vort, amrex::Real dt)
{
    Real u_x = (u(i+1, j, k, 0) - u(i-1, j, k, 0)) / (2 * dt);
    Real u_y = (u(i, j+1, k, 0) - u(i, j-1, k, 0)) / (2 * dt);
    Real u_z = (u(i, j, k+1, 0) - u(i, j, k-1, 0)) / (2 * dt);
    Real v_x = (u(i+1, j, k, 1) - u(i-1, j, k, 1)) / (2 * dt);
    Real v_y = (u(i, j+1, k, 1) - u(i, j-1, k, 1)) / (2 * dt);
    Real v_z = (u(i, j, k+1, 1) - u(i, j, k-1, 1)) / (2 * dt);
    Real w_x = (u(i+1, j, k, 2) - u(i-1, j, k, 2)) / (2 * dt);
    Real w_y = (u(i, j+1, k, 2) - u(i, j-1, k, 2)) / (2 * dt);
    Real w_z = (u(i, j, k+1, 2) - u(i, j, k-1, 2)) / (2 * dt);

    Real WX = w_y - v_z;
    Real WY = u_z - w_x;
    Real WZ = v_x - u_y;

    Real Q_criterion = -0.5 * (u_x*u_x + v_y*v_y + w_z*w_z) - u_y*v_x - u_z*w_x - v_z*w_y;
    Real Vorticity   = sqrt(WX * WX + WY * WY + WZ * WZ);

    vort(i, j, k, 0) = Q_criterion; //Vorticity;
}

AMREX_GPU_DEVICE
AMREX_FORCE_INLINE
void
compute_shear(int i, int j, int k, amrex::Array4<amrex::Real> const& u,
                  amrex::Array4<amrex::Real> const& shear, amrex::Real dt)
{
    Real SS, Q_CSM, E_CSM, F_CSM, C_CSM;

    Real u_x = (u(i+1, j, k, 0) - u(i-1, j, k, 0)) / (2 * dt);
    Real u_y = (u(i, j+1, k, 0) - u(i, j-1, k, 0)) / (2 * dt);
    Real u_z = (u(i, j, k+1, 0) - u(i, j, k-1, 0)) / (2 * dt);
    Real v_x = (u(i+1, j, k, 1) - u(i-1, j, k, 1)) / (2 * dt);
    Real v_y = (u(i, j+1, k, 1) - u(i, j-1, k, 1)) / (2 * dt);
    Real v_z = (u(i, j, k+1, 1) - u(i, j, k-1, 1)) / (2 * dt);
    Real w_x = (u(i+1, j, k, 2) - u(i-1, j, k, 2)) / (2 * dt);
    Real w_y = (u(i, j+1, k, 2) - u(i, j-1, k, 2)) / (2 * dt);
    Real w_z = (u(i, j, k+1, 2) - u(i, j, k-1, 2)) / (2 * dt);

    Real XX = u_x;
    Real YY = v_y;
    Real ZZ = w_z;
    Real XY = 0.5 * (u_y + v_x);
    Real XZ = 0.5 * (u_z + w_x);
    Real YZ = 0.5 * (v_z + w_y);

    SS = 2.0 * (XX * XX + XY * XY + XZ * XZ +
                XY * XY + YY * YY + YZ * YZ +
                XZ * XZ + YZ * YZ + ZZ * ZZ);

    Q_CSM = (-0.5) * (u_x * u_x + u_y * v_x + u_z * w_x +
                        v_x * u_y + v_y * v_y + v_z * w_y +
                        w_x * u_z + w_y * v_z + w_z * w_z);

    E_CSM = 0.5 * (u_x * u_x + v_x * v_x + w_x * w_x +
                    u_y * u_y + v_y * v_y + w_y * w_y +
                    u_z * u_z + v_z * v_z + w_z * w_z);
    if (E_CSM != 0.0)
        F_CSM = Q_CSM / E_CSM;
    else
        F_CSM = 0.0;
    if (F_CSM < (-1.0))
        F_CSM = -1.0;
    if (F_CSM > (1.0))
        F_CSM = 1.0;
        C_CSM = 1.0 / 22.0 * pow(fabs(F_CSM), 1.5) * (1.0 - F_CSM);

    shear(i, j, k, 0) = C_CSM * dt * dt * sqrt(SS);  //不知道这样直接从三维改到二维对不对
}





// 周期性边界条件下不需要手动边界处理
// AMReX会自动处理周期性边界
AMREX_GPU_DEVICE
AMREX_FORCE_INLINE
void
fill_boundary(int i, int j, int k, amrex::Array4<amrex::Real> const& fold,
              amrex::Array4<amrex::Real> const& fnew, amrex::IntVect const& hi)
{
    // 对于周期性边界条件，不需要特殊的边界处理
    // AMReX的FillBoundary函数会自动处理周期性边界
    return;

    // 以下代码在周期性边界条件下不再需要
    /*
    if(i == 0)
    {
        int x = i;
        int y = j;
        int z = k;

        int x1 = x+1;
        int y1 = y;
        int z1 = z;

        Real uxt1 = 0.0, uyt1 = 0.0, uzt1 = 0.0, rhot1 = 0.0, ft1 = 0.0;
        Real uxt = 0.0, uyt = 0.0, uzt = 0.0, rhot = 0.0;

        for(int q = 0; q < Q; q++)
        {
            ft1    = fold(x1, y1, z1, q);
            rhot1 += ft1;
            uxt1  += ft1 * e[q][0];
            uyt1  += ft1 * e[q][1];
            uzt1  += ft1 * e[q][2];
        }

        uxt1 /= p0;
        uyt1 /= p0;
        uzt1 /= p0;

        uxt = 0.0;
        uyt = 0.0;
        uzt = 0.0;

        // uxt = uxt1;
        // uyt = uyt1;
        // uzt = uzt1;

        rhot = rhot1;

        for(int q = 0; q < Q; q++)
        {
            fnew(x, y, z, q) = feqHeLuo(rhot, {uxt, uyt, uzt}, q) + fold(x1, y1, z1, q) - feqHeLuo(rhot1, {uxt1, uyt1, uzt1}, q);
        }
    }

    if(i == hi[0]) //右边界
    {
        int x = i;
        int y = j;
        int z = k;

        int x1 = x-1;
        int y1 = y;
        int z1 = z;

        Real uxt1 = 0.0, uyt1 = 0.0, uzt1 = 0.0, rhot1 = 0.0, ft1 = 0.0;
        Real uxt = 0.0, uyt = 0.0, uzt = 0.0, rhot = 0.0;

        for(int q = 0; q < Q; q++)
        {
            ft1    = fold(x1, y1, z1, q);
            rhot1 += ft1;
            uxt1  += ft1 * e[q][0];
            uyt1  += ft1 * e[q][1];
            uzt1  += ft1 * e[q][2];
        }

        uxt1 /= p0;
        uyt1 /= p0;
        uzt1 /= p0;

        uxt = 0.0;
        uyt = 0.0;
        uzt = 0.0;

        // uxt = uxt1;
        // uyt = uyt1;
        // uzt = uzt1;

        rhot = rhot1;

        for(int q = 0; q < Q; q++)
        {
            fnew(x, y, z, q) = feqHeLuo(rhot, {uxt, uyt, uzt}, q) + fold(x1, y1, z1, q) - feqHeLuo(rhot1, {uxt1, uyt1, uzt1}, q);
        }
    }

    if(j == 0)
    {
        int x = i;
        int y = j;
        int z = k;

        int x1 = x;
        int y1 = y+1;
        int z1 = z;

        Real uxt1 = 0.0, uyt1 = 0.0, uzt1 = 0.0, rhot1 = 0.0, ft1 = 0.0;
        Real uxt = 0.0, uyt = 0.0, uzt = 0.0, rhot = 0.0;

        for(int q = 0; q < Q; q++)
        {
            ft1    = fold(x1, y1, z1, q);
            rhot1 += ft1;
            uxt1  += ft1 * e[q][0];
            uyt1  += ft1 * e[q][1];
            uzt1  += ft1 * e[q][2];
        }

        uxt1 /= p0;
        uyt1 /= p0;
        uzt1 /= p0;

        uxt = 0.0;
        uyt = 0.0;
        uzt = 0.0;

        // uxt = uxt1;
        // uyt = uyt1;
        // uzt = uzt1;

        rhot = rhot1;

        for(int q = 0; q < Q; q++)
        {
            fnew(x, y, z, q) = feqHeLuo(rhot, {uxt, uyt, uzt}, q) + fold(x1, y1, z1, q) - feqHeLuo(rhot1, {uxt1, uyt1, uzt1}, q);
        }
    }

    if(j == hi[1])
    {
        int x = i;
        int y = j;
        int z = k;

        int x1 = x;
        int y1 = y-1;
        int z1 = z;

        Real uxt1 = 0.0, uyt1 = 0.0, uzt1 = 0.0, rhot1 = 0.0, ft1 = 0.0;
        Real uxt = 0.0, uyt = 0.0, uzt = 0.0, rhot = 0.0;

        for(int q = 0; q < Q; q++)
        {
            ft1    = fold(x1, y1, z1, q);
            rhot1 += ft1;
            uxt1  += ft1 * e[q][0];
            uyt1  += ft1 * e[q][1];
            uzt1  += ft1 * e[q][2];
        }

        uxt1 /= p0;
        uyt1 /= p0;
        uzt1 /= p0;

        uxt = 0.0;
        uyt = 0.0;
        uzt = 0.0;

        // uxt = uxt1;
        // uyt = uyt1;
        // uzt = uzt1;

        rhot = rhot1;

        for(int q = 0; q < Q; q++)
        {
            fnew(x, y, z, q) = feqHeLuo(rhot, {uxt, uyt, uzt}, q) + fold(x1, y1, z1, q) - feqHeLuo(rhot1, {uxt1, uyt1, uzt1}, q);
        }
    }

    if(k == 0)
    {
        int x = i;
        int y = j;
        int z = k;

        int x1 = x;
        int y1 = y;
        int z1 = z+1;

        Real uxt1 = 0.0, uyt1 = 0.0, uzt1 = 0.0, rhot1 = 0.0, ft1 = 0.0;
        Real uxt = 0.0, uyt = 0.0, uzt = 0.0, rhot = 0.0;

        for(int q = 0; q < Q; q++)
        {
            ft1    = fold(x1, y1, z1, q);
            rhot1 += ft1;
            uxt1  += ft1 * e[q][0];
            uyt1  += ft1 * e[q][1];
            uzt1  += ft1 * e[q][2];
        }

        uxt1 /= p0;
        uyt1 /= p0;
        uzt1 /= p0;

        uxt = 0.0;
        uyt = 0.0;
        uzt = 0.0;

        // uxt = uxt1;
        // uyt = uyt1;
        // uzt = uzt1;

        rhot = rhot1;

        for(int q = 0; q < Q; q++)
        {
            fnew(x, y, z, q) = feqHeLuo(rhot, {uxt, uyt, uzt}, q) + fold(x1, y1, z1, q) - feqHeLuo(rhot1, {uxt1, uyt1, uzt1}, q);
        }
    }

    if(k == hi[2])
    {
        int x = i;
        int y = j;
        int z = k;

        int x1 = x;
        int y1 = y;
        int z1 = z-1;

        Real uxt1 = 0.0, uyt1 = 0.0, uzt1 = 0.0, rhot1 = 0.0, ft1 = 0.0;
        Real uxt = 0.0, uyt = 0.0, uzt = 0.0, rhot = 0.0;

        for(int q = 0; q < Q; q++)
        {
            ft1    = fold(x1, y1, z1, q);
            rhot1 += ft1;
            uxt1  += ft1 * e[q][0];
            uyt1  += ft1 * e[q][1];
            uzt1  += ft1 * e[q][2];
        }

        uxt1 /= p0;
        uyt1 /= p0;
        uzt1 /= p0;

        uxt = 0.0;
        uyt = 0.0;
        uzt = 0.0;

        // uxt = uxt1;
        // uyt = uyt1;
        // uzt = uzt1;

        rhot = rhot1;

        for(int q = 0; q < Q; q++)
        {
            fnew(x, y, z, q) = feqHeLuo(rhot, {uxt, uyt, uzt}, q) + fold(x1, y1, z1, q) - feqHeLuo(rhot1, {uxt1, uyt1, uzt1}, q);
        }
    }


    if(i < 0 || i > hi[0] || j < 0 || j > hi[1] || k < 0 || k > hi[2])
    {
        for(int q = 0; q < Q; q++)
        {
            fnew(i, j, k, q) = 0.0;
            fold(i, j, k, q) = 0.0;
        }
    }
    */
}

AMREX_GPU_DEVICE
AMREX_FORCE_INLINE
void
collide(int i, int j, int k, amrex::Array4<amrex::Real> const& fold, amrex::Array4<amrex::Real> const& s,
        amrex::Array4<amrex::Real> const&Ft, amrex::Real tau_lev, amrex::Real dt, amrex::IntVect const& hi)
{
    Real uxt = 0.0, uyt = 0.0, uzt = 0.0, rhot = 0.0, ft = 0.0;
    Real Ftx = 0.0, Fty = 0.0, Ftz = 0.0;

    s(i, j, k, 0) = 0.0;

    Real tauStar = tau_lev + 3.0 * s(i, j, k, 0) / cs2 / dt;
    Real omega   = 1.0 / tauStar;

    for(int q = 0; q < Q; q++)
    {
        ft    = fold(i, j, k, q);
        rhot += ft;
        uxt  += ft * e[q][0];
        uyt  += ft * e[q][1];
        uzt  += ft * e[q][2];
    }

    Ftx = Ft(i, j, k, 0);
    Fty = Ft(i, j, k, 1);
    Ftz = Ft(i, j, k, 2);

    uxt /= p0;
    uyt /= p0;
    uzt /= p0;

    uxt += 0.5 * dt * Ftx / (p0 / (cs * cs));
    uyt += 0.5 * dt * Fty / (p0 / (cs * cs));
    uzt += 0.5 * dt * Ftz / (p0 / (cs * cs));

    // if((i >= 1) && (i < hi[0]) && (j >= 1) && (j < hi[1]))
    // {
        for(int q = 0; q < Q; q++)
        {
            Real forceEx = (1 - 0.5 * omega) * dt * forceGuo({uxt, uyt, uzt}, {Ftx, Fty, Ftz}, q) * cs2;
            fold(i, j, k, q) = (1 - omega) * fold(i, j, k, q) + omega * feqHeLuo(rhot, {uxt, uyt, uzt}, q) + forceEx;
        }
    // }
}

AMREX_GPU_DEVICE
AMREX_FORCE_INLINE
void
stream(int i, int j, int k, amrex::Array4<amrex::Real> const& fold, amrex::Array4<amrex::Real> const& fnew,
      amrex::IntVect const& hi, const bool is_finest)
{
    for(int q = 0; q < Q; q++)
    {
        int xm = i - e[q][0];
        int ym = j - e[q][1];
        int zm = k - e[q][2];

        // if(is_finest)
        // {
        //     if((i >= 1) && (i < hi[0]) && (j >= 1) && (j < hi[1]))
        //     {
        //         fnew(i, j, k, q) = fold(xm, ym, zm, q);
        //     }
        // }
        // else
        // {
        //     fnew(i, j, k, q) = fold(xm, ym, zm, q);
        // }
        if((i >= 1) && (i < hi[0]) && (j >= 1) && (j < hi[1]) && (k >= 1) && (k < hi[2]))
        {
            fnew(i, j, k, q) = fold(xm, ym, zm, q);
        }

    }
}

AMREX_GPU_DEVICE
AMREX_FORCE_INLINE
void
swap_ddf(int i, int j, int k, amrex::Array4<amrex::Real> const& fold,
         amrex::Array4<amrex::Real> const& fnew)
{
    for(int q = 0; q < Q; q++)
    {
        fold(i, j, k, q) = fnew(i, j, k, q);
    }
}


AMREX_GPU_DEVICE
AMREX_FORCE_INLINE
void
interp_scale(int i, int j, int k, amrex::Array4<amrex::Real> const& fold,
             amrex::Array4<amrex::Real> const& fnew, amrex::Real scale)
{
    Real uxt = 0.0, uyt = 0.0, uzt = 0.0, rhot = 0.0, ft = 0.0, res = 0.0;

    for(int q = 0; q < Q; q++)
    {
        ft    = fold(i, j, k, q);
        rhot += ft;
        uxt  += ft * e[q][0];
        uyt  += ft * e[q][1];
        uzt  += ft * e[q][2];
    }

    uxt  = uxt / p0;
    uyt  = uyt / p0;
    uzt  = uzt / p0;

    for(int q = 0; q < Q; q++)
    {
        res = feqHeLuo(rhot, {uxt, uyt, uzt}, q);
        fnew(i, j, k, q) =  res + (fold(i, j, k, q) - res) * scale;
    }
}




AMREX_GPU_DEVICE
AMREX_FORCE_INLINE
void
average_scale(int i, int j, int k, amrex::Array4<amrex::Real> const& fold, amrex::Real scale)
{

    Real uxt = 0.0, uyt = 0.0, uzt = 0.0, rhot = 0.0, ft = 0.0, res = 0.0;

    for(int q = 0; q < Q; q++)
    {
        ft    = fold(i, j, k, q);
        rhot += ft;
        uxt  += ft * e[q][0];
        uyt  += ft * e[q][1];
        uzt  += ft * e[q][2];
    }

    uxt  = uxt / p0;
    uyt  = uyt / p0;
    uzt  = uzt / p0;

    for(int q = 0; q < Q; q++)
    {
        res = feqHeLuo(rhot, {uxt, uyt, uzt}, q);
        fold(i, j, k, q) =  res + (fold(i, j, k, q) - res) * scale;
    }
}


struct AmrCoreFill
{
    AMREX_GPU_DEVICE
    void operator() (const amrex::IntVect& /*iv*/, amrex::Array4<amrex::Real> const& /*data*/,
                     const int /*dcomp*/, const int /*numcomp*/,
                     amrex::GeometryData const& /*geom*/, const amrex::Real /*time*/,
                     const amrex::BCRec* /*bcr*/, const int /*bcomp*/,
                     const int /*orig_comp*/) const
        {
            // do something for external Dirichlet (BCType::ext_dir)
        }
};



AMREX_GPU_HOST_DEVICE    //自适应加密
AMREX_FORCE_INLINE
void
state_error (int i, int j, int k,
             amrex::Array4<char> const& tag,
             amrex::Array4<amrex::Real const> const& vort,
             amrex::Real phierr, char tagval, char clearval, int lev, amrex::Real dx,
             amrex::IntVect const& lo2, amrex::IntVect const& hi2, amrex::RealVect const& pos)
{
    Real x_r = pos[0] * dx_0 / dx;
    Real y_r = pos[1] * dx_0 / dx;
    Real z_r = pos[2] * dx_0 / dx;

    Real r_r = R * dx_0 / dx;

    Real dist = sqrt((i - x_r)*(i - x_r) + (j - y_r)*(j - y_r) + (k - z_r)*(k - z_r));


    //根据固体表面距离加密
    if(dist <= r_r+5)
    {
        tag(i,j,k) = tagval;
    }


    // 网格自适应加密
    if(lev == 0)
    {
        if(
            vort(i, j, k, 0) >= 0.0005 && vort(i, j, k, 0) <= 0.001
            // &&
            // i >= lo2[0] && i <= hi2[0] && j >= lo2[1] && j <= hi2[1] && j >= lo2[1] && j <= hi2[1]
           )

        {
            tag(i,j,k) = tagval;
        }
    }
    else if(lev == 1)
    {
        if(
            vort(i, j, k, 0) >= 0.001 && vort(i, j, k, 0) <= 0.005
            // &&
            // i >= lo2[0] && i <= hi2[0] && j >= lo2[1] && j <= hi2[1] && j >= lo2[1] && j <= hi2[1]
           )

        {
            tag(i,j,k) = tagval;
        }
    }
    else if(lev == 2)
    {
        if(
            vort(i, j, k, 0) >= 0.005 && vort(i, j, k, 0) <= 0.01
            // &&
            // i >= lo2[0] && i <= hi2[0] && j >= lo2[1] && j <= hi2[1] && j >= lo2[1] && j <= hi2[1]
           )

        {
            tag(i,j,k) = tagval;
        }
    }
    else if(lev == 3)
    {
        if(
            vort(i, j, k, 0) >= 0.005 && vort(i, j, k, 0) <= 0.01
            // &&
            // i >= lo2[0] && i <= hi2[0] && j >= lo2[1] && j <= hi2[1] && j >= lo2[1] && j <= hi2[1]
           )

        {
            tag(i,j,k) = tagval;
        }
    }
}


AMREX_GPU_HOST_DEVICE      //局部加密
AMREX_FORCE_INLINE
void
state_error_2 (int i, int j, int k,
             amrex::Array4<char> const& tag,
             amrex::Array4<amrex::Real const> const& vort,
             amrex::Real phierr, char tagval, char clearval, int lev, amrex::Real dx,
             amrex::IntVect const& lo2, amrex::IntVect const& hi2, amrex::RealVect const& pos)
{
    Real x_r = pos[0] * dx_0 / dx;
    Real y_r = pos[1] * dx_0 / dx;
    Real z_r = pos[2] * dx_0 / dx;

    Real r_r = R * dx_0 / dx;

    Real dist = sqrt((i - x_r)*(i - x_r) + (j - y_r)*(j - y_r) + (k - z_r)*(k - z_r));


    //根据固体表面距离加密
    if(dist <= r_r+5)
    {
        tag(i,j,k) = tagval;
    }

    // 网格自适应加密
    if(lev == 0)
    {
        if(
            vort(i, j, k, 0) >= 0.00000005 && vort(i, j, k, 0) <= 0.000000051
           )

        {
            tag(i,j,k) = tagval;
        }
    }
    else if(lev == 1)
    {
        if(
            vort(i, j, k, 0) >= 0.00000005 && vort(i, j, k, 0) <= 0.000000051
           )

        {
            tag(i,j,k) = tagval;
        }
    }
    else if(lev == 2)
    {
        if(
            vort(i, j, k, 0) >= 0.00000005 && vort(i, j, k, 0) <= 0.000000051
           )

        {
            tag(i,j,k) = tagval;
        }
    }
    else if(lev == 3)
    {
        if(
            vort(i, j, k, 0) >= 0.00000005 && vort(i, j, k, 0) <= 0.000000051
           )

        {
            tag(i,j,k) = tagval;
        }
    }

    if(i >= (nx-2*D)/dx || i <= 2*D/dx || j >= (ny-2*D)/dx || j <= 2*D/dx)
    {
        tag(i,j,k) = clearval;
    }
}


AMREX_GPU_HOST_DEVICE    //自适应加密
AMREX_FORCE_INLINE
void
state_error_3 (int i, int j, int k,
             amrex::Array4<char> const& tag,
             amrex::Array4<amrex::Real const> const& vort,
             amrex::Real phierr, char tagval, char clearval, int lev, amrex::GeometryData const& geomdata,
             amrex::IntVect const& lo2, amrex::IntVect const& hi2, amrex::RealVect* p_pos, const int particle_num)
{
    const amrex::Real* dx = geomdata.CellSize();

    // 根据固体表面距离加密
    for(int p_num = 0; p_num < particle_num; p_num++)
    {
        Real x_r = p_pos[p_num][0] * dx_0 / dx[0];
        Real y_r = p_pos[p_num][1] * dx_0 / dx[0];
        Real z_r = p_pos[p_num][2] * dx_0 / dx[0];

        Real r_r = R * dx_0 / dx[0];

        Real dist = sqrt((i - x_r)*(i - x_r) + (j - y_r)*(j - y_r) + (k - z_r)*(k - z_r));

        if(dist <= r_r+3)
        {
            tag(i,j,k) = tagval;
        }
    }


    // if(i >= lo2[0] && i <= hi2[0] && j >= lo2[1] && j <= hi2[1] && k >= lo2[2] && k <= hi2[2])
    // {
    //     tag(i,j,k) = tagval;
    // }

    // 网格自适应加密
    // if(vort(i, j, k, 0) >= 0.00001 && vort(i, j, k, 0) <= 0.000011)
    // {
    //     tag(i,j,k) = tagval;
    // }


    // if(i <= 10 || i >= geomdata.Domain().length(0)-10
    // || j <= 10 || j >= geomdata.Domain().length(1)-10
    // || k <= 10 || k >= geomdata.Domain().length(2)-10
    // )
    // {
    //     tag(i,j,k) = tagval;
    // }
}



//********************************************************************//
//                         IBM   function                             //
//********************************************************************//
AMREX_GPU_HOST_DEVICE
AMREX_FORCE_INLINE
Real delta3p(amrex::Real r)
{
    r = fabs(r);
    if (r <= 0.5)
        return ((1.0 + sqrt(1.0 - 3.0 * r * r)) / 3.0);
    else if (r <= 1.5)
        return ((5.0 - 3.0 * r - sqrt(1.0 - 3.0 * (1.0 - r) * (1.0 - r))) / 6.0);
    else
        return 0.0;
}



template <typename P>
AMREX_GPU_HOST_DEVICE
AMREX_FORCE_INLINE
void
force_interp_extrap(P const& p,
                    amrex::Real& fx,  amrex::Real& fy,  amrex::Real& fz,
                    amrex::Real& tx,  amrex::Real& ty,  amrex::Real& tz,
                    amrex::Real& x_local,  amrex::Real& y_local,  amrex::Real& z_local,
                    amrex::Real& area,
                    amrex::Array4<amrex::Real> const& u, amrex::Array4<amrex::Real> const& rho, amrex::Array4<amrex::Real> const& Ft,
                    amrex::Real const delta, amrex::RealVect const& uc, amrex::RealVect const& wc, amrex::RealVect const& centre)
{
    amrex::Real xt, yt, zt, lx, ly, lz;
    amrex::Real IB_Interp, IB_weight;
    amrex::Real uxt, uyt, uzt, rhot, upx, upy, upz, fxt, fyt, fzt;

    xt = p.pos(0) / delta;
    yt = p.pos(1) / delta;
    zt = p.pos(2) / delta;

    IB_weight = LP_dr * area;

    uxt = 0.0;
    uyt = 0.0;
    uzt = 0.0;
    rhot = 0.0;

    upx = uc[0] + wc[2] * (-y_local) + wc[1] * ( z_local);
    upy = uc[1] + wc[2] * ( x_local) + wc[0] * (-z_local);
    upz = uc[2] + wc[0] * ( y_local) + wc[1] * (-x_local);

    for(int x = -2; x <= 2; x++)
    {
        for(int y = -2; y <= 2; y++)
        {
            for(int z = -2; z <= 2; z++)
            {
                int xx = static_cast<int>(amrex::Math::floor(xt)) + x; //拉格朗日点作用范围内真实的点坐标
                int yy = static_cast<int>(amrex::Math::floor(yt)) + y;
                int zz = static_cast<int>(amrex::Math::floor(zt)) + z;

                lx = xt - (xx + 0.5);
                ly = yt - (yy + 0.5);
                lz = zt - (zz + 0.5);

                IB_Interp = delta3p(lx) * delta3p(ly) * delta3p(lz);

                uxt += u(xx, yy, zz, 0) * IB_Interp;
                uyt += u(xx, yy, zz, 1) * IB_Interp;
                uzt += u(xx, yy, zz, 2) * IB_Interp;
                rhot += rho(xx, yy, zz, 0) * IB_Interp;
            }
        }
    }

    rhot = 2 * rhot / cs2 / dt_min;

    fxt = rhot * (uxt - upx);
    fyt = rhot * (uyt - upy);
    fzt = rhot * (uzt - upz);

    fx  = fxt * IB_weight * dx_min * dx_min * dx_min;
    fy  = fyt * IB_weight * dx_min * dx_min * dx_min;
    fz  = fzt * IB_weight * dx_min * dx_min * dx_min;

    tx = (fzt * y_local - fyt * z_local) * IB_weight * dx_min * dx_min * dx_min;
    ty = (fxt * z_local - fzt * x_local) * IB_weight * dx_min * dx_min * dx_min;
    tz = (fyt * x_local - fxt * y_local) * IB_weight * dx_min * dx_min * dx_min;

    for(int x = -2; x <= 2; x++)
    {
        for(int y = -2; y <= 2; y++)
        {
            for(int z = -2; z <= 2; z++)
            {
                int xx = static_cast<int>(amrex::Math::floor(xt)) + x;
                int yy = static_cast<int>(amrex::Math::floor(yt)) + y;
                int zz = static_cast<int>(amrex::Math::floor(zt)) + z;

                lx = xt - (xx + 0.5);
                ly = yt - (yy + 0.5);
                lz = zt - (zz + 0.5);

                IB_Interp = delta3p(lx) * delta3p(ly) * delta3p(lz) * IB_weight;

                amrex::Gpu::Atomic::AddNoRet(&Ft(xx, yy, zz, 0), -fxt * IB_Interp);
                amrex::Gpu::Atomic::AddNoRet(&Ft(xx, yy, zz, 1), -fyt * IB_Interp);
                amrex::Gpu::Atomic::AddNoRet(&Ft(xx, yy, zz, 2), -fzt * IB_Interp);
            }
        }
    }
}



















#endif