#ifndef InitParticles_H_
#define InitParticles_H_

#include <AMReX_Config.H>
#include <cstdlib>
#include <ctime>


void generateSpheres(int n, amrex::Real R, amrex::Real NX, amrex::Real NY, amrex::Real NZ, amrex::Vector<amrex::RealVect>& sphere_centers)
{
    // 初始化随机数生成器
    std::srand(std::time(0));

    amrex::Real min_dist = 8 * R;  // 球与球及球与盒子壁面的最小距离

    // 生成球心的循环
    for (int i = 0; i < n; ++i) {
        bool valid = false;
        amrex::RealVect new_center;

        // 保证新生成的球心符合要求
        while (!valid) {
            // 在有效范围内生成随机球心坐标 (x, y, z)
            amrex::Real x = min_dist + static_cast<amrex::Real>(std::rand()) / RAND_MAX * (NX - 2 * min_dist);
            amrex::Real y = min_dist + static_cast<amrex::Real>(std::rand()) / RAND_MAX * (NY - 2 * min_dist);
            amrex::Real z = min_dist + static_cast<amrex::Real>(std::rand()) / RAND_MAX * (NZ - 2 * min_dist);

            new_center = amrex::RealVect{x, y, z};

            // 检查新球心是否与之前的球保持足够的距离
            valid = true;
            for (const auto& center : sphere_centers) {
                amrex::Real distance = std::sqrt(std::pow(center[0] - x, 2) +
                                                 std::pow(center[1] - y, 2) +
                                                 std::pow(center[2] - z, 2));
                if (distance < 2 * R + min_dist) {
                    valid = false;  // 距离太近，重新生成
                    break;
                }
            }
        }

        // 将合法的球心添加到向量中
        sphere_centers.push_back(new_center);
    }
}

void generateSpheres(amrex::Real D, amrex::Real NX, amrex::Real NY, amrex::Real NZ, amrex::Vector<amrex::RealVect>& sphere_centers)
{
    // 在扩大的计算域中均匀分布颗粒
    // 计算每个方向上可以放置的颗粒数量
    amrex::Real min_spacing = 2.5 * D;  // 颗粒间最小间距

    int n_x = static_cast<int>((NX - 2 * D) / min_spacing);  // x方向颗粒数
    int n_y = static_cast<int>((NY - 2 * D) / min_spacing);  // y方向颗粒数
    int n_z = static_cast<int>((NZ - 2 * D) / min_spacing);  // z方向颗粒数

    // 确保颗粒数量不超过500
    int total_particles = n_x * n_y * n_z;
    if(total_particles > 500) {
        // 按比例缩减
        amrex::Real scale = std::pow(500.0 / total_particles, 1.0/3.0);
        n_x = static_cast<int>(n_x * scale);
        n_y = static_cast<int>(n_y * scale);
        n_z = static_cast<int>(n_z * scale);
    }

    amrex::Real x_spacing = (NX - 2 * D) / (n_x + 1); // x 方向球心间距
    amrex::Real y_spacing = (NY - 2 * D) / (n_y + 1); // y 方向球心间距
    amrex::Real z_spacing = (NZ - 2 * D) / (n_z + 1); // z 方向球心间距

    // 在三维空间中均匀分布颗粒
    for (int k = 0; k < n_z; ++k) {
        for (int i = 0; i < n_x; ++i) {
            for (int j = 0; j < n_y; ++j) {
                amrex::Real x = D + (i + 1) * x_spacing; // 当前球的 x 坐标
                amrex::Real y = D + (j + 1) * y_spacing; // 当前球的 y 坐标
                amrex::Real z = D + (k + 1) * z_spacing; // 当前球的 z 坐标

                // 添加当前球心坐标到 sphere_centers
                sphere_centers.push_back(amrex::RealVect{x, y, z});

                // 如果达到500个颗粒就停止
                if(sphere_centers.size() >= 500) {
                    return;
                }
            }
        }
    }

    // 打印实际生成的颗粒数量
    std::cout << "Generated " << sphere_centers.size() << " particles in domain "
              << NX << "x" << NY << "x" << NZ << std::endl;
    std::cout << "Particle grid: " << n_x << "x" << n_y << "x" << n_z << std::endl;
}






#endif