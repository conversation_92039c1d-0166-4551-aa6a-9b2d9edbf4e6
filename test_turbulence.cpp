// 简单的湍流生成测试程序
#include <iostream>
#include <fstream>
#include <cmath>

#define PI 3.141592653589793238462643383279

// 从D3Q19.H复制的参数
const double D = 16.0;
const double NX = 12 * D;
const double NY = 12 * D; 
const double NZ = 48 * D;
const double nx = NX;
const double ny = NY;
const double nz = NZ;
const double dx_0 = nx / NX;
const double cs = 1.0 / sqrt(3.0);
const double u_rms = 0.1 * cs;

// 湍流生成函数（简化版本，用于测试）
void generate_divergence_free_turbulence(int i, int j, int k, double& u_turb, double& v_turb, double& w_turb)
{
    double x = i * dx_0;
    double y = j * dx_0;
    double z = k * dx_0;
    
    u_turb = 0.0;
    v_turb = 0.0;
    w_turb = 0.0;
    
    // 多模态叠加，确保散度为零
    for(int n = 1; n <= 6; n++)
    {
        double kx = 2.0 * PI * n / nx;
        double ky = 2.0 * PI * n / ny;
        double kz = 2.0 * PI * n / nz;
        
        double k_mag = sqrt(kx*kx + ky*ky + kz*kz);
        if(k_mag < 1e-10) continue;
        
        // Kolmogorov能谱 E(k) ~ k^(-5/3)
        double amplitude = u_rms * pow(k_mag, -5.0/6.0) / (n * n);
        
        // 不同的相位确保随机性
        double phase1 = 0.1 * n * (i + 2*j + 3*k);
        double phase2 = 0.2 * n * (2*i + j + 3*k);
        double phase3 = 0.3 * n * (3*i + 2*j + k);
        
        // 使用旋度形式确保散度为零
        double psi = amplitude * sin(kx*x + phase1) * cos(ky*y + phase2) * sin(kz*z + phase3);
        double phi = amplitude * cos(kx*x + phase1) * sin(ky*y + phase2) * cos(kz*z + phase3);
        
        // 计算偏导数
        double dpsi_dy = -amplitude * ky * sin(kx*x + phase1) * sin(ky*y + phase2) * sin(kz*z + phase3);
        double dphi_dz = -amplitude * kz * cos(kx*x + phase1) * sin(ky*y + phase2) * sin(kz*z + phase3);
        
        double dphi_dx = -amplitude * kx * sin(kx*x + phase1) * sin(ky*y + phase2) * cos(kz*z + phase3);
        double dpsi_dz = amplitude * kz * sin(kx*x + phase1) * cos(ky*y + phase2) * cos(kz*z + phase3);
        
        double dpsi_dx = amplitude * kx * cos(kx*x + phase1) * cos(ky*y + phase2) * sin(kz*z + phase3);
        double dphi_dy = amplitude * ky * cos(kx*x + phase1) * cos(ky*y + phase2) * cos(kz*z + phase3);
        
        u_turb += dpsi_dy - dphi_dz;
        v_turb += dphi_dx - dpsi_dz;
        w_turb += dpsi_dx - dphi_dy;
    }
    
    // 限制速度幅值以保持数值稳定性
    double vel_mag = sqrt(u_turb*u_turb + v_turb*v_turb + w_turb*w_turb);
    if(vel_mag > 0.3 * cs)  // 限制在声速的30%以内
    {
        double scale = 0.3 * cs / vel_mag;
        u_turb *= scale;
        v_turb *= scale;
        w_turb *= scale;
    }
}

int main()
{
    std::cout << "测试湍流生成函数..." << std::endl;
    
    // 测试参数
    std::cout << "网格尺寸: " << NX << " x " << NY << " x " << NZ << std::endl;
    std::cout << "特征速度 u_rms: " << u_rms << std::endl;
    std::cout << "声速 cs: " << cs << std::endl;
    
    // 生成一个小的测试区域
    int test_size = 32;
    double u_sum = 0.0, v_sum = 0.0, w_sum = 0.0;
    double u_rms_calc = 0.0, v_rms_calc = 0.0, w_rms_calc = 0.0;
    double div_max = 0.0;
    int count = 0;
    
    std::ofstream file("turbulence_test.dat");
    file << "# i j k u v w div\n";
    
    for(int i = 1; i < test_size-1; i++)
    {
        for(int j = 1; j < test_size-1; j++)
        {
            for(int k = 1; k < test_size-1; k++)
            {
                double u, v, w;
                generate_divergence_free_turbulence(i, j, k, u, v, w);
                
                u_sum += u;
                v_sum += v;
                w_sum += w;
                
                u_rms_calc += u*u;
                v_rms_calc += v*v;
                w_rms_calc += w*w;
                
                // 计算散度（简单差分）
                double u_p, v_p, w_p, u_m, v_m, w_m;
                generate_divergence_free_turbulence(i+1, j, k, u_p, v_p, w_p);
                generate_divergence_free_turbulence(i-1, j, k, u_m, v_m, w_m);
                double du_dx = (u_p - u_m) / (2.0 * dx_0);
                
                generate_divergence_free_turbulence(i, j+1, k, u_p, v_p, w_p);
                generate_divergence_free_turbulence(i, j-1, k, u_m, v_m, w_m);
                double dv_dy = (v_p - v_m) / (2.0 * dx_0);
                
                generate_divergence_free_turbulence(i, j, k+1, u_p, v_p, w_p);
                generate_divergence_free_turbulence(i, j, k-1, u_m, v_m, w_m);
                double dw_dz = (w_p - w_m) / (2.0 * dx_0);
                
                double div = du_dx + dv_dy + dw_dz;
                div_max = std::max(div_max, std::abs(div));
                
                file << i << " " << j << " " << k << " " << u << " " << v << " " << w << " " << div << "\n";
                count++;
            }
        }
    }
    
    file.close();
    
    // 计算统计量
    u_sum /= count;
    v_sum /= count;
    w_sum /= count;
    
    u_rms_calc = sqrt(u_rms_calc / count);
    v_rms_calc = sqrt(v_rms_calc / count);
    w_rms_calc = sqrt(w_rms_calc / count);
    
    std::cout << "\n统计结果:" << std::endl;
    std::cout << "平均速度: u=" << u_sum << ", v=" << v_sum << ", w=" << w_sum << std::endl;
    std::cout << "RMS速度: u_rms=" << u_rms_calc << ", v_rms=" << v_rms_calc << ", w_rms=" << w_rms_calc << std::endl;
    std::cout << "总RMS: " << sqrt((u_rms_calc*u_rms_calc + v_rms_calc*v_rms_calc + w_rms_calc*w_rms_calc)/3.0) << std::endl;
    std::cout << "目标RMS: " << u_rms << std::endl;
    std::cout << "最大散度: " << div_max << std::endl;
    
    std::cout << "\n测试完成！结果保存在 turbulence_test.dat" << std::endl;
    
    return 0;
}
