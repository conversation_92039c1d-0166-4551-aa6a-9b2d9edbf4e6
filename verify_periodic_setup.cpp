// 验证周期性边界设置和颗粒分布的简单程序
#include <iostream>
#include <vector>
#include <cmath>

// 从D3Q19.H复制的参数
const double D = 16.0;
const double NX = 24 * D;  // 384
const double NY = 24 * D;  // 384
const double NZ = 24 * D;  // 384

// 简化的颗粒生成函数
void generateSpheres(double D, double NX, double NY, double NZ, std::vector<std::vector<double>>& sphere_centers) 
{
    double min_spacing = 2.5 * D;
    
    int n_x = static_cast<int>((NX - 2 * D) / min_spacing);
    int n_y = static_cast<int>((NY - 2 * D) / min_spacing);  
    int n_z = static_cast<int>((NZ - 2 * D) / min_spacing);
    
    std::cout << "Initial grid calculation:" << std::endl;
    std::cout << "n_x = " << n_x << ", n_y = " << n_y << ", n_z = " << n_z << std::endl;
    std::cout << "Total particles (before limit) = " << n_x * n_y * n_z << std::endl;
    
    // 确保颗粒数量不超过500
    int total_particles = n_x * n_y * n_z;
    if(total_particles > 500) {
        double scale = std::pow(500.0 / total_particles, 1.0/3.0);
        n_x = static_cast<int>(n_x * scale);
        n_y = static_cast<int>(n_y * scale);
        n_z = static_cast<int>(n_z * scale);
        std::cout << "Scaled down to: n_x = " << n_x << ", n_y = " << n_y << ", n_z = " << n_z << std::endl;
    }
    
    double x_spacing = (NX - 2 * D) / (n_x + 1);
    double y_spacing = (NY - 2 * D) / (n_y + 1);
    double z_spacing = (NZ - 2 * D) / (n_z + 1);
    
    std::cout << "Spacing: x = " << x_spacing << ", y = " << y_spacing << ", z = " << z_spacing << std::endl;
    std::cout << "Min spacing required: " << min_spacing << std::endl;
    
    // 生成颗粒位置
    for (int k = 0; k < n_z; ++k) {
        for (int i = 0; i < n_x; ++i) {
            for (int j = 0; j < n_y; ++j) {
                double x = D + (i + 1) * x_spacing;
                double y = D + (j + 1) * y_spacing;
                double z = D + (k + 1) * z_spacing;
                
                sphere_centers.push_back({x, y, z});
                
                if(sphere_centers.size() >= 500) {
                    return;
                }
            }
        }
    }
}

int main()
{
    std::cout << "=== 周期性边界条件和颗粒分布验证 ===" << std::endl;
    
    // 1. 计算域信息
    std::cout << "\n1. 计算域信息:" << std::endl;
    std::cout << "颗粒直径 D = " << D << std::endl;
    std::cout << "计算域尺寸: " << NX << " x " << NY << " x " << NZ << std::endl;
    std::cout << "计算域体积: " << NX * NY * NZ << std::endl;
    std::cout << "网格点数: " << NX * NY * NZ << " (约 " << (NX * NY * NZ) / 1e6 << " 百万)" << std::endl;
    
    // 2. 周期性边界条件验证
    std::cout << "\n2. 周期性边界条件:" << std::endl;
    std::cout << "所有方向 (x, y, z) 都设置为周期性边界" << std::endl;
    std::cout << "边界类型: BCType::int_dir" << std::endl;
    std::cout << "几何周期性: {1, 1, 1}" << std::endl;
    
    // 3. 颗粒分布验证
    std::cout << "\n3. 颗粒分布分析:" << std::endl;
    std::vector<std::vector<double>> particles;
    generateSpheres(D, NX, NY, NZ, particles);
    
    std::cout << "实际生成颗粒数: " << particles.size() << std::endl;
    
    // 计算颗粒分布统计
    if(!particles.empty()) {
        double x_min = particles[0][0], x_max = particles[0][0];
        double y_min = particles[0][1], y_max = particles[0][1];
        double z_min = particles[0][2], z_max = particles[0][2];
        
        double x_sum = 0, y_sum = 0, z_sum = 0;
        
        for(const auto& p : particles) {
            x_min = std::min(x_min, p[0]);
            x_max = std::max(x_max, p[0]);
            y_min = std::min(y_min, p[1]);
            y_max = std::max(y_max, p[1]);
            z_min = std::min(z_min, p[2]);
            z_max = std::max(z_max, p[2]);
            
            x_sum += p[0];
            y_sum += p[1];
            z_sum += p[2];
        }
        
        double x_center = x_sum / particles.size();
        double y_center = y_sum / particles.size();
        double z_center = z_sum / particles.size();
        
        std::cout << "颗粒分布范围:" << std::endl;
        std::cout << "  X: [" << x_min << ", " << x_max << "], 中心: " << x_center << std::endl;
        std::cout << "  Y: [" << y_min << ", " << y_max << "], 中心: " << y_center << std::endl;
        std::cout << "  Z: [" << z_min << ", " << z_max << "], 中心: " << z_center << std::endl;
        
        std::cout << "计算域中心: (" << NX/2 << ", " << NY/2 << ", " << NZ/2 << ")" << std::endl;
        
        // 检查最小距离
        double min_dist = 1e10;
        for(size_t i = 0; i < particles.size(); ++i) {
            for(size_t j = i + 1; j < particles.size(); ++j) {
                double dx = particles[i][0] - particles[j][0];
                double dy = particles[i][1] - particles[j][1];
                double dz = particles[i][2] - particles[j][2];
                double dist = sqrt(dx*dx + dy*dy + dz*dz);
                min_dist = std::min(min_dist, dist);
            }
        }
        
        std::cout << "颗粒间最小距离: " << min_dist << std::endl;
        std::cout << "要求最小距离: " << 2.5 * D << std::endl;
        
        if(min_dist >= 2.0 * D) {
            std::cout << "✓ 颗粒间距检查通过" << std::endl;
        } else {
            std::cout << "✗ 警告：颗粒间距过小" << std::endl;
        }
    }
    
    // 4. 湍流参数验证
    std::cout << "\n4. 湍流参数:" << std::endl;
    double cs = 1.0 / sqrt(3.0);
    double u_rms = 0.1 * cs;
    double lambda_taylor = D * 0.5;
    double Re_lambda = 100.0;
    double mv_0 = u_rms * lambda_taylor / Re_lambda;
    
    std::cout << "声速 cs = " << cs << std::endl;
    std::cout << "湍流脉动速度 u_rms = " << u_rms << std::endl;
    std::cout << "泰勒微尺度 λ = " << lambda_taylor << std::endl;
    std::cout << "泰勒雷诺数 Re_λ = " << Re_lambda << std::endl;
    std::cout << "运动粘度 ν = " << mv_0 << std::endl;
    
    // 5. 计算资源估算
    std::cout << "\n5. 计算资源估算:" << std::endl;
    double total_cells = NX * NY * NZ;
    double memory_per_cell = 19 * 8; // 19个分布函数，每个8字节
    double total_memory_gb = total_cells * memory_per_cell / (1024.0 * 1024.0 * 1024.0);
    
    std::cout << "总网格点数: " << total_cells << std::endl;
    std::cout << "估算内存需求: " << total_memory_gb << " GB (仅分布函数)" << std::endl;
    std::cout << "建议并行进程数: " << static_cast<int>(total_cells / 1e6) << " - " << static_cast<int>(total_cells / 5e5) << std::endl;
    
    std::cout << "\n=== 验证完成 ===" << std::endl;
    
    return 0;
}
