# *****************************************************************
# 周期性边界条件湍流多颗粒仿真参数 - 泰勒雷诺数约100
# 扩大计算域，均匀分布颗粒
# Run until nsteps == max_step or time == stop_time,
#     whichever comes first
# *****************************************************************
max_step  = 50000   # 减少步数以适应更大的计算域
stop_time = 10000000.0

amr.regrid_int  = 10  # 进一步增加重网格间隔，周期性边界下更稳定

amr.grid_eff          = 0.8  # 提高网格效率
amr.n_error_buf       = 0 0 0 0
amr.max_grid_size     = 64 64 64 64  # 增大网格块尺寸以适应更大计算域
amr.blocking_factor_x = 32 32 32 32
amr.blocking_factor_y = 32 32 32 32
amr.blocking_factor_z = 32 32 32 32

amr.plot_file  = periodic_turbulent_500_particle_
amr.plot_int   = 500  # 适当增加输出间隔
amr.begin_plot = 0

lbm.err = 0.3 0.5 0.7 0.9  # 调整误差阈值以适应周期性边界

# *****************************************************************
# mesh setting
#
# *****************************************************************
# geometry.prob_lo     =  0.0  0.0  0.0
# geometry.prob_hi     =  2.0  1.0  1.0
# geometry.is_periodic =  0    0    0

# amr.n_cell           =  64 32 32
# amr.max_level        =  0

# amr.v                =  1
# amr.ref_ratio        =  2 2 2 2

# amr.blocking_factor_x = 8
# amr.blocking_factor_y = 8
# amr.blocking_factor_z = 8
# *****************************************************************
# Particles
# *****************************************************************
